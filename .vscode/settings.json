{"[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[css]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[html]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "security.workspace.trust.untrustedFiles": "open", "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "editor.formatOnSave": true, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "explorer.confirmDragAndDrop": false, "javascript.updateImportsOnFileMove.enabled": "always", "typescript.updateImportsOnFileMove.enabled": "always", "[less]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[vue]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "codeium.enableCodeLens": false, "workbench.startupEditor": "none", "diffEditor.ignoreTrimWhitespace": false, "files.autoSave": "after<PERSON>elay", "editor.defaultFormatter": "esbenp.prettier-vscode", "i18n-ally.localesPaths": ["src/locales", "src/locales/lang", "public/resource/tinymce/langs"]}