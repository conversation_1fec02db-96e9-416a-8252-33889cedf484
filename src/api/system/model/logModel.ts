import { BasicPageParams, BasicFetchResult } from '../../model/baseModel'

export type DataListParams = {
  name?: string
}

export type DataParams = {
  id?: string
  user_id: number
  user_id_name: string
  action: string
  url: string
  remark: string
  type: string
  ip: string
  created_at: string
  updated_at: string
  status: number
}

export type DataPageParams = BasicPageParams & DataParams

export interface DataListItem {
  id: number
  url: string
  value: string
  desc: string
}

export type LogListGetResultModel = BasicFetchResult<DataListItem>
