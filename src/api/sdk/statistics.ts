import { SubmitAutoReplyModel, DelAutoReplyModel } from './model/quickReplyModel'
import { useSdkSelectAreaStoreWithOut } from '/@/store/modules/sdkSelectArea'
const { getSelectArea } = useSdkSelectAreaStoreWithOut()
import { defHttp } from '/@/utils/http/axios'

enum Api {
  dataStatisticsListApi = '/sdk/sdk/customerService/stat',
  categoryStatisticsListApi = '/sdk/sdk/customerService/statCategory',
}

export class StatisticsApi {
  static dataStatisticsListPage = (params) => {
    return defHttp.get<any>({
      url: Api.dataStatisticsListApi,
      params: { connection: getSelectArea, ...params },
    })
  }
  static categoryStatisticsListPage = (params) => {
    return defHttp.get<any>({
      url: Api.categoryStatisticsListApi,
      params: { connection: getSelectArea, ...params },
    })
  }
}
