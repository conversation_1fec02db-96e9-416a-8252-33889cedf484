import {
  SubmitAutoReplyModel,
  DelAutoReplyModel,
  GetSmartReplyModel,
} from './model/quickReplyModel'
import { useSdkSelectAreaStoreWithOut } from '/@/store/modules/sdkSelectArea'
const { getSelectArea } = useSdkSelectAreaStoreWithOut()
import { defHttp } from '/@/utils/http/axios'

enum Api {
  ListApi = '/sdk/sdk/customerService/autoReply',
  getSmartReplyApi = '/sdk/sdk/customerService/getSmartReply',
  submitAutoReplyApi = '/sdk/sdk/customerService/submitAutoReply',
  delAutoReplyApi = '/sdk/sdk/customerService/delAutoReply',
}

export class QuickReplytApi {
  static getListPage = (params) => {
    return defHttp.get<any>({
      url: Api.ListApi,
      params: { connection: getSelectArea, ...params },
    })
  }

  static getSmartReply = (params: GetSmartReplyModel) => {
    return defHttp.post<any>({
      url: Api.getSmartReplyApi,
      params: { ...params },
    })
  }

  static submitAutoReply = (params: SubmitAutoReplyModel) => {
    return defHttp.post<any>({
      url: Api.submitAutoReplyApi,
      params: { connection: getSelectArea, ...params },
    })
  }

  static delAutoReply = (params: DelAutoReplyModel) => {
    return defHttp.post<any>({
      url: Api.delAutoReplyApi,
      params: { connection: getSelectArea, ...params },
    })
  }
}
