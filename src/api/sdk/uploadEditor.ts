import { defHttp } from '/@/utils/http/axios'
import { useSdkSelectAreaStoreWithOut } from '/@/store/modules/sdkSelectArea'
const { getSelectArea } = useSdkSelectAreaStoreWithOut()

enum Api {
  getListPage = '/sdk/sdk/staticHtmlCdnFiles/index',
  submit = '/sdk/sdk/staticHtmlCdnFiles/submit',
  uploadToOss = '/uploadToTxOss',
}
export class UploadEditorApi {
  static getListPage = (params: any) => {
    return defHttp.get<any>({
      url: Api.getListPage,
      params: { connection: getSelectArea, ...params },
    })
  }
  static submit = (params: any) => {
    return defHttp.post<any>({
      url: Api.submit,
      params,
    })
  }
  static uploadToOss = (params: any) => {
    return defHttp.post<any>({
      url: Api.uploadToOss,
      params,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  }
}
