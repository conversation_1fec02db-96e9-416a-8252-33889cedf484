import {
  ListPageParams,
  QuestionDetailModel,
  ReplyQuestion,
  RemarkQuestion,
  Classify,
  Withdraw,
  TranslateReply,
} from './model/customerOrderModel'
import { useSdkSelectAreaStoreWithOut } from '/@/store/modules/sdkSelectArea'
const { getSelectArea } = useSdkSelectAreaStoreWithOut()
import { defHttp } from '/@/utils/http/axios'

enum Api {
  ListApi = '/sdk/sdk/customerService/questions',
  DetailApi = '/sdk/sdk/customerService/questionDetail',
  CategoriesApi = '/sdk/sdk/customerService/categories',
  ReplyQuestionApi = '/sdk/sdk/customerService/reply',
  RemarkQuestionApi = '/sdk/sdk/customerService/remark',
  ClassifyApi = '/sdk/sdk/customerService/classify',
  withdrawApi = '/sdk/sdk/customerService/withdraw',
  LanguageApi = '/sdk/sdk/customerService/getTargetLanguage',
  TranslateReplyApi = '/sdk/sdk/customerService/translateReply',
}

export class CustomerOrdertApi {
  static getListPage = (params: ListPageParams) => {
    return defHttp.get<any>({
      url: Api.ListApi,
      params: { connection: getSelectArea, ...params },
    })
  }
  static getQuestionDetail = (params: QuestionDetailModel) => {
    return defHttp.get<any>({
      url: Api.DetailApi,
      params: { connection: getSelectArea, ...params },
    })
  }
  static getCategories = (params) => {
    return defHttp.get<any>({
      url: Api.CategoriesApi,
      params: { connection: getSelectArea, ...params },
    })
  }
  static getLanguage = () => {
    return defHttp.get<any>({
      url: Api.LanguageApi,
      params: { connection: getSelectArea },
    })
  }
  static translateLanguage = (params: TranslateReply) => {
    return defHttp.post<any>({
      url: Api.TranslateReplyApi,
      params: { connection: getSelectArea, ...params },
    })
  }
  static replyQuestion = (params: ReplyQuestion) => {
    return defHttp.post<any>({
      url: Api.ReplyQuestionApi,
      params: { connection: getSelectArea, ...params },
    })
  }
  static remarkQuestion = (params: RemarkQuestion) => {
    return defHttp.post<any>({
      url: Api.RemarkQuestionApi,
      params: { connection: getSelectArea, ...params },
    })
  }
  static classify = (params: Classify) => {
    return defHttp.post<any>({
      url: Api.ClassifyApi,
      params: { connection: getSelectArea, ...params },
    })
  }
  static withdraw = (params: Withdraw) => {
    return defHttp.post<any>({
      url: Api.withdrawApi,
      params: { connection: getSelectArea, ...params },
    })
  }
}
