import { ListPageParams, DetailModel } from './model/testAccountModel'
import { useSdkSelectAreaStoreWithOut } from '/@/store/modules/sdkSelectArea'
const { getSelectArea } = useSdkSelectAreaStoreWithOut()
import { defHttp } from '/@/utils/http/axios'

enum Api {
  ListApi = '/sdk/sdk/testAccount/index',
  SubmitApi = '/sdk/sdk/testAccount/submit',
}

export const getListPage = (params: ListPageParams) =>
  defHttp.get<DetailModel>({ url: Api.ListApi, params: { connection: getSelectArea, ...params } })

export const Submit = (params: DetailModel) =>
  defHttp.post({ url: Api.SubmitApi, params: { connection: getSelectArea, ...params } })
