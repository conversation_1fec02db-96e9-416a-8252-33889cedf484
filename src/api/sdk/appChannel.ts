import { ListPageParams, DetailModel } from './model/appChannelModel'
import { useSdkSelectAreaStoreWithOut } from '/@/store/modules/sdkSelectArea'
const { getSelectArea } = useSdkSelectAreaStoreWithOut()
import { defHttp } from '/@/utils/http/axios'

enum Api {
  ListApi = '/sdk/sdk/appChannel/index',
  SubmitApi = '/sdk/sdk/appChannel/submit',
}

export const getListPage = (params: ListPageParams) =>
  defHttp.get<DetailModel>({ url: Api.ListApi, params: { connection: getSelectArea, ...params } })

export const Submit = (params: DetailModel) =>
  defHttp.post({ url: Api.SubmitApi, params: { connection: getSelectArea, ...params } })
