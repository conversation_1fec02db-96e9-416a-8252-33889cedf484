import { ListPageParams, SubmitCategorieModel } from './model/categoryModel'
import { useSdkSelectAreaStoreWithOut } from '/@/store/modules/sdkSelectArea'
const { getSelectArea } = useSdkSelectAreaStoreWithOut()
import { defHttp } from '/@/utils/http/axios'

enum Api {
  ListApi = '/sdk/sdk/customerService/categories',
  SubmitCategorieApi = '/sdk/sdk/customerService/submitCategories',
}

export class CategoryApi {
  static getListPage = (params: ListPageParams) => {
    return defHttp.get<any>({
      url: Api.ListApi,
      params: { connection: getSelectArea, ...params },
    })
  }
  static submitCategorie = (params: SubmitCategorieModel) => {
    return defHttp.get<any>({
      url: Api.SubmitCategorieApi,
      params: { connection: getSelectArea, ...params },
    })
  }
}
