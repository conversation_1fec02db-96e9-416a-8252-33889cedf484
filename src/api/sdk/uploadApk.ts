import { defHttp } from '/@/utils/http/axios'
import { useSdkSelectAreaStoreWithOut } from '/@/store/modules/sdkSelectArea'
const { getSelectArea } = useSdkSelectAreaStoreWithOut()

enum Api {
  getTempOssKey = '/sdk/sdk/staticCdnFiles/getTempOssKey',
  submit = '/sdk/sdk/staticCdnFiles/submit',
  getListPage = '/sdk/sdk/staticCdnFiles/index',
}
export class UploadApkApi {
  static getTempOssKey = (params: any = {}) => {
    return defHttp.get<any>({
      url: Api.getTempOssKey,
      params: { connection: getSelectArea, ...params },
    })
  }
  static submit = (params: any) => {
    return defHttp.get<any>({
      url: Api.submit,
      params: { connection: getSelectArea, ...params },
    })
  }
  static getListPage = (params: any) => {
    return defHttp.get<any>({
      url: Api.getListPage,
      params: { connection: getSelectArea, ...params },
    })
  }
}
