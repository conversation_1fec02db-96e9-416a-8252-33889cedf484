import { ListPageParams, DetailModel } from './model/faqModel'
import { useSdkSelectAreaStoreWithOut } from '/@/store/modules/sdkSelectArea'
const { getSelectArea } = useSdkSelectAreaStoreWithOut()
import { defHttp } from '/@/utils/http/axios'

enum Api {
  ListApi = '/sdk/sdk/faq/index',
  SubmitApi = '/sdk/sdk/faq/submit',
  DeleteApi = '/sdk/sdk/faq/delete',
}

export const getListPage = (params: ListPageParams) =>
  defHttp.get<DetailModel>({ url: Api.ListApi, params: { connection: getSelectArea, ...params } })

export const Submit = (params: DetailModel) =>
  defHttp.post({ url: Api.SubmitApi, params: { connection: getSelectArea, ...params } })

export const Delete = (params: DetailModel) =>
  defHttp.post({ url: Api.DeleteApi, params: { connection: getSelectArea, ...params } })