import { defHttp } from '/@/utils/http/axios'
import { useSdkSelectAreaStoreWithOut } from '/@/store/modules/sdkSelectArea'
const { getSelectArea } = useSdkSelectAreaStoreWithOut()

enum Api {
  getListPage = '/sdk/sdk/staticWebsiteTemplate/index',
  submit = '/sdk/sdk/staticWebsiteTemplate/submit',
  uploadToTxOss = '/uploadToTxOss',
}
export class TemplateHtmlApi {
  static getListPage = (params: any) => {
    return defHttp.get<any>({
      url: Api.getListPage,
      params: { connection: getSelectArea, ...params },
    })
  }
  static submit = (params: any) => {
    return defHttp.post<any>({
      url: Api.submit,
      params,
    })
  }
  static uploadToTxOss = (params: any) => {
    return defHttp.post<any>({
      url: Api.uploadToTxOss,
      params,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  }
}
