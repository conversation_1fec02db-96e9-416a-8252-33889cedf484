import { ContentTypeEnum } from '/@/enums/httpEnum'
import { useSdkSelectAreaStoreWithOut } from '/@/store/modules/sdkSelectArea'
const { getSelectArea } = useSdkSelectAreaStoreWithOut()

import { defHttp } from '/@/utils/http/axios'

enum Api {
  ListURL = '/sdk/vip/users/index',
  SubmitURL = '/sdk/vip/users/submit',

  PackageListURL = '/sdk/vip/packages/index',
  PackageSubmitURL = '/sdk/vip/packages/submit',

  ConfigListURL = '/sdk/vip/config/index',
  ConfigSubmitURL = '/sdk/vip/config/submit',

  packageGrantLogURL = '/sdk/vip/packageGrantLog/index',

  popBecomeVipPackageNumURL = '/sdk/vip/packages/popBecomeVipPackageNum',

  recordURL = '/sdk/vip/users/record',
}

export class VipApi {
  static getListPage = (params: any) => {
    return defHttp.get<any>({ url: Api.ListURL, params: { connection: getSelectArea, ...params } })
  }

  static Submit = (params: any) => {
    return defHttp.post({ url: Api.SubmitURL, params: { connection: getSelectArea, ...params } })
  }

  static getConifgListPage = (params: any) => {
    return defHttp.get<any>({
      url: Api.ConfigListURL,
      params: { connection: getSelectArea, ...params },
    })
  }

  static ConfigSubmit = (params: any) => {
    const data = Object.fromEntries(
      Object.entries(params).map(([key, value]) => [key, value === null ? '' : value]),
    )
    return defHttp.post({
      url: Api.ConfigSubmitURL,
      params: { connection: getSelectArea, ...data },
    })
  }

  static getPackageList = (params: any) => {
    return defHttp.get<any>({
      url: Api.PackageListURL,
      params: { connection: getSelectArea, ...params },
    })
  }

  static packageSubmit = (params: any) => {
    return defHttp.post<any>({
      url: Api.PackageSubmitURL,
      headers: { 'Content-Type': ContentTypeEnum.FORM_DATA },
      data: params,
      // @ts-ignore
      ignoreCancelToken: true,
    })
  }

  static getPackageGrantLogList = (params: any) => {
    return defHttp.get<any>({
      url: Api.packageGrantLogURL,
      params: { connection: getSelectArea, ...params },
    })
  }

  static popBecomeVipPackageNum = (params: any) => {
    return defHttp.post({
      url: Api.popBecomeVipPackageNumURL,
      params: { connection: getSelectArea, ...params },
    })
  }

  //入库
  static record = (params: any) => {
    return defHttp.post({
      url: Api.recordURL,
      params: { connection: getSelectArea, ...params },
    })
  }
}
