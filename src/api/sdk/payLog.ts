import { ListPageParams } from './model/payLogModel'
import { useSdkSelectAreaStoreWithOut } from '/@/store/modules/sdkSelectArea'
const { getSelectArea } = useSdkSelectAreaStoreWithOut()
import { defHttp } from '/@/utils/http/axios'

enum Api {
  ListApi = '/sdk/sdk/payLog/index',
}

export const getListPage = (params: ListPageParams) =>
  defHttp.get<any>({ url: Api.ListApi, params: { connection: getSelectArea, ...params } })
