import { defHttp } from '/@/utils/http/axios'
import { useSdkSelectAreaStoreWithOut } from '/@/store/modules/sdkSelectArea'
const { getSelectArea } = useSdkSelectAreaStoreWithOut()

enum Api {
  purgeCdn = '/sdk/sdk/staticCdnFiles/purgeCdn',
}
export class CdnApi {
  static purgeCdn = (params: any) => {
    console.log(params)
    return defHttp.get<any>({
      url: Api.purgeCdn,
      params: { connection: getSelectArea, ...params },
    })
  }
}
