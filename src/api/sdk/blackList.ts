import { ListPageParams } from './model/blackListModel'
import { useSdkSelectAreaStoreWithOut } from '/@/store/modules/sdkSelectArea'
const { getSelectArea } = useSdkSelectAreaStoreWithOut()
import { defHttp } from '/@/utils/http/axios'

enum Api {
  ListApi = '/sdk/sdk/blacklist/index',
  SubmitApi = '/sdk/sdk/blacklist/submit',
  DeleteApi = '/sdk/sdk/blacklist/delete',
}

export class BlackListApi {
  static getListPage = (params: ListPageParams) => {
    return defHttp.get<any>({
      url: Api.ListApi,
      params: { connection: getSelectArea, ...params },
    })
  }

  static submit = (params: any) => {
    return defHttp.post({ url: Api.SubmitApi, params: { connection: getSelectArea, ...params } })
  }

  static delete = (params: any) => {
    return defHttp.post({ url: Api.DeleteApi, params: { connection: getSelectArea, ...params } })
  }
}
