import { BasicPageParams } from '../../model/baseModel'

export type DetailModel = {
  id: number // 主键，自增,
  created_at_start: string // 开始时间
  created_at_end: string // 结束时间
}

export type QuestionDetailModel = {
  id: number | string // 详情id
}

export type ReplyQuestion = {
  id: number | string // 详情id
  content?: string // 内容回复
}

export type RemarkQuestion = {
  id: number | string // 详情id
  remark?: string // 备注回复
}

export type Classify = {
  id: number
  category_id: number | ''
  remark: string
}

export type Withdraw = {
  replie_id: number
}

export type TranslateReply = {
  source_text: string
  target_lang: string
  source_lang?: string
}

export type ListPageParams = BasicPageParams & DetailModel
