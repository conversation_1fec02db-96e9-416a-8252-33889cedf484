import { BasicPageParams } from '../../model/baseModel'

export type DetailModel = {
  appid: number // bigint 在TypeScript中通常用 number 表示
  appkey: string
  client_id: number
  game_id: number
  app_type_id: number
  type: 1 | 2 | 3 // tinyint 可以是1, 2, 或 3
  name: string
  website: string
  icon: string
  is_platform: 0 | 1 // tinyint 可以是0或1
  callback_url: string
  wechat_proof_id: number
  wechat_open_proof_id: number
  qq_proof_id: number
  weibo_proof_id: number
  wechat_mini_proof_id: number
  google_proof_id?: number // 可选字段
  facebook_proof_id?: number
  gamecenter_proof_id?: number
  naver_proof_id?: number
  remark: string
  package_name: string
  ref_appid: number
  screen_orientation: string // 假设是 'landscape' 或 'portrait'
  pay_resource_id: string
  pay_url: string
  pay_server_callback: number
  pay_client_callback: number
  pay_h5: number
  pay_subject_id: number
  version: number
  created_at: number // 假设这是时间戳
  updated_at: number // 假设这是时间戳
  updated_by: string
  project_id?: number // 可选字段
  platform_id?: number
  platform_name?: string
  use_pay_sdk: 1 // 默认值为1，表示启用，因此这里不使用 ?
}

export type ListPageParams = BasicPageParams & DetailModel
