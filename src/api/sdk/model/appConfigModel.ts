import { BasicPageParams } from '../../model/baseModel'

export type DetailModel = {
  id: number
  login_methods: string // 假设登录方式配置是一个字符串数组
  function_switches: string // 假设功能开关配置是一个字符串，用逗号分隔的数字
  pay_switches: string // 假设支付渠道开关也是一个字符串，用逗号分隔的数字
  product_list: string // 假设商品信息是一个JSON字符串
  third_login: string // 假设第三方登录配置是一个JSON字符串
  third_data: string // 假设第三方统计配置也是一个JSON字符串
  float_panel: string // 假设悬浮窗配置是一个字符串
  updated_by?: string // 更新者，可选字段
  created_at?: Date // 创建时间，可选字段
  updated_at?: Date // 更新时间，可选字段
  appid: number // 应用ID
}

export type ListPageParams = BasicPageParams & DetailModel
