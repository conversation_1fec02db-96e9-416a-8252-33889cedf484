import { BasicPageParams } from '../../model/baseModel'

export type DetailModel = {
  id: bigint // 自增id
  user_id: string // 用户id
  order_id: string // 订单id
  game_uid: string // 游戏用户id
  appid: string // 游戏id
  project_id?: number // 项目id，可选字段
  client_id: number // client_id
  currency: string // 币种
  money: number // 消费金额
  money_usd: number // 美元金额
  coins: string // 游戏币
  status: number // 支付状态
  pay_time: number // 支付时间
  refund_time: number // 退款时间
  pay_resource_id: string // 支付资源id
  channel: string // 支付渠道
  channel_order_id: string // 渠道订单号
  bank_id: number // 支付银行id
  app_key: string // 商户编号
  role_id: string // 角色id
  role_name: string // 角色名称
  product_id: string // 商品id
  product_name: string // 商品名称
  server_id: string // 区服id
  server_name: string // 区服名称
  app_order_id: string // 游戏订单号
  purchase_type: number // 购买类型，默认为1
  parent_order_id: string // 父订单id
  subscribe_expire_time: number // 订阅到期时间
  subscribe_cancel_time: number // 订阅取消时间
  ip: string // 支付ip
  uuid: string // 设备号
  manufacturer: string // 设备品牌
  sand_box: number // 是否沙盒
  is_test: number // 是否测试账号产生
  order_from: number // 订单来源
  created_at: number // 下单时间
  updated_at: number // 更新时间
}

export type ListPageParams = BasicPageParams & DetailModel
