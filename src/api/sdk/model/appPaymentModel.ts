import { BasicPageParams } from '../../model/baseModel'

export type DetailModel = {
  id: number // 自增ID
  appid: number // 应用id，bigint在TypeScript中通常用number表示
  project_id: number // 项目ID
  payload: string // 支付配置，varchar类型在TypeScript中用string表示
  created_at: number // 创建时间，这里假设存储的是时间戳
  created_by: string // 创建者
  updated_at: number // 更新时间，这里同样假设存储的是时间戳
  updated_by: string // 更新者
}

export type ListPageParams = BasicPageParams & DetailModel
