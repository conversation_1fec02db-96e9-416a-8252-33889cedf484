import { BasicPageParams } from '../../model/baseModel'

export type DetailModel = {
  id: number // 主键，自增
  type?: number // 黑名单类型，1: user_id, 2: idfa，可空
  user_id: string // 当黑名单类型为1时是user_id，为2时为idfa
  appid?: number // 应用ID，当appid=0时表示全局，可空
  remark: string // 备注
  created_at?: number // 创建时间，Unix时间戳，可空
  updated_at?: number // 更新时间，Unix时间戳，可空
  created_by?: string // 创建者，可空
  updated_by?: string // 更新者，可空
}

export type ListPageParams = BasicPageParams & DetailModel
