import { BasicPageParams } from '../model/baseModel'

import { defHttp } from '/@/utils/http/axios'
import { useSdkSelectAreaStoreWithOut } from '/@/store/modules/sdkSelectArea'
const { getSelectArea } = useSdkSelectAreaStoreWithOut()

enum Api {
  ListApi = '/sdk/sdk/userinfo/index',
}

export const getListPage = (params: BasicPageParams) =>
  defHttp.get<any>({ url: Api.ListApi, params: { connection: getSelectArea, ...params } })
