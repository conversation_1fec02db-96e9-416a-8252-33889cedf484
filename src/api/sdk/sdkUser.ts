import { useSdkSelectAreaStoreWithOut } from '/@/store/modules/sdkSelectArea'
import { defHttp } from '/@/utils/http/axios'
const { getSelectArea } = useSdkSelectAreaStoreWithOut()

enum Api {
  ListApi = '/sdk/sdk/sdkUser/index',
  SubmitApi = '/sdk/sdk/sdkUser/submit',
}

export class SDKUserApi {
  static index(params: any) {
    return defHttp.get<any>({ url: Api.ListApi, params: { connection: getSelectArea, ...params } })
  }

  static submit(params: any) {
    return defHttp.get<any>({
      url: Api.SubmitApi,
      params: { connection: getSelectArea, ...params },
    })
  }
}
