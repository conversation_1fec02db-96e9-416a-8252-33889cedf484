import { ListPageParams, DetailModel } from './model/retainModel'

import { defHttp } from '/@/utils/http/axios'

enum Api {
  ChargeListApi = '/stat/retain/charge',
  LoginListApi = '/stat/retain/login',
}

export const getChargeRetainListPage = (params: ListPageParams) =>
  defHttp.get<DetailModel>({ url: Api.ChargeListApi, params })

export const getLoginRetainListPage = (params: ListPageParams) =>
  defHttp.get<DetailModel>({ url: Api.LoginListApi, params })
