import { ListPageParams, DetailModel } from './model/payModel'

import { defHttp } from '/@/utils/http/axios'

enum Api {
  WeeklyListApi = '/stat/ltv/weekly',
  DailyListApi = '/stat/ltv/daily',
}

export const getWeeklyListPage = (params: ListPageParams) =>
  defHttp.get<DetailModel>({ url: Api.WeeklyListApi, params })

export const getDailyListPage = (params: ListPageParams) =>
  defHttp.get<DetailModel>({ url: Api.DailyListApi, params })
