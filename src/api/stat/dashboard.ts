import { ListPageParams, DetailModel } from './model/payModel'

import { defHttp } from '/@/utils/http/axios'

enum Api {
  hourlyListApi = '/stat/dashboard/hourly',
  DailyListApi = '/stat/dashboard/daily',
}

export const getHourlyListPage = (params: ListPageParams) =>
  defHttp.get<DetailModel>({ url: Api.hourlyListApi, params })

export const getDailyListPage = (params: ListPageParams) =>
  defHttp.get<DetailModel>({ url: Api.DailyListApi, params })
