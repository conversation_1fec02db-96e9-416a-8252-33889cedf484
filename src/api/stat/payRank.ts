import { useSdkSelectAreaStoreWithOut } from '/@/store/modules/sdkSelectArea'
import { defHttp } from '/@/utils/http/axios'
const { getSelectArea } = useSdkSelectAreaStoreWithOut()

enum Api {
  PayRankListApi = '/stat/payOrder/payRank',
  NewPayRankListApi = '/stat/payOrder/newPayRank',
}

export const getPayRankListPage = (params: any) => {
  return defHttp.get<any>({
    url: Api.PayRankListApi,
    params: { ...params, connection: getSelectArea },
  })
}

export const getNewPayRankListPage = (params: any) =>
  defHttp.get<any>({ url: Api.NewPayRankListApi, params: { ...params, connection: getSelectArea } })
