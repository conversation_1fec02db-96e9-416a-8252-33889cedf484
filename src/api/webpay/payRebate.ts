import { ListPageParams, DetailModel } from './model/payRebateModel'
import { useSdkSelectAreaStoreWithOut } from '/@/store/modules/sdkSelectArea'
const { getSelectArea } = useSdkSelectAreaStoreWithOut()
import { defHttp } from '/@/utils/http/axios'

enum Api {
  ListApi = '/sdk/sdk/payRebate/index',
  UpdateStatusApi = '/sdk/sdk/payRebate/updateStatus',
  SubmitApi = '/sdk/sdk/payRebate/submit',
}

export const getListPage = (params: ListPageParams) =>
  defHttp.get<DetailModel>({ url: Api.ListApi, params: { connection: getSelectArea, ...params } })

export const UpdateStatus = (id: number, status: number) =>
  defHttp.get<DetailModel>({
    url: Api.UpdateStatusApi,
    params: { connection: getSelectArea, id: id, status: status },
  })

export const Submit = (params: DetailModel) =>
  defHttp.post({ url: Api.SubmitApi, params: { connection: getSelectArea, ...params } })
