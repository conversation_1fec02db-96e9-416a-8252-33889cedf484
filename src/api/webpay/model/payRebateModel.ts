import { BasicPageParams } from '../../model/baseModel'

export type DetailModel = {
  id: number // ID
  project_id: number // 游戏ID，默认值为0
  pay_with: string // 支付方式，默认为空字符串
  ratio: number // 返利比例，默认为0
  status: number // 状态：0-禁用，1-启用，默认为1
  start_time: Date // 生效时间，默认为1970-01-01 08:00:00
  end_time: Date // 截止时间，默认为1970-01-01 08:00:00
  created_at: Date // 添加时间，创建时自动设置并更新
  updated_at: Date // 更新时间，更新时自动设置
}

export type ListPageParams = BasicPageParams & DetailModel
