import { BasicPageParams } from '../../model/baseModel'

export type DetailModel = {
  id: number // ID
  parent_id: number // 父渠道ID，默认为0
  pay_with_name: string // 支付名称
  pay_with_code: string // 渠道识别码，默认为空字符串
  sort: number // 排序，默认为0
  rate: number // 渠道费率，默认为0.0000
  status: number // 状态，1:开启, 0:关闭，默认为1
  created_at?: Date // 添加时间，可空
  updated_at: Date // 更新时间，创建时自动设置并更新
  icon_url: string // logo，默认为空字符串
  is_show: number // 显示类型：0=不显示，1=Web，默认为0
}

export type ListPageParams = BasicPageParams & DetailModel
