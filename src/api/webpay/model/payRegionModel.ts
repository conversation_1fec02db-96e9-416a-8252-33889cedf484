import { BasicPageParams } from '../../model/baseModel'

export type DetailModel = {
  id: number // ID
  region_name: string // 地区名，默认为'0'
  region_tag: string // 地区标签
  currency: string // 货币，默认为空字符串
  region_en_name: string // 地区英文名，默认为'0'
  country_flag: string // 国旗旗帜，默认为空字符串
  sort: number // 排序，默认为0
  pay_switch_config: string // 支付渠道开关配置
  created_at?: Date // 添加时间，可空
  updated_at: Date // 更新时间，创建时自动设置并更新
}

export type ListPageParams = BasicPageParams & DetailModel
