import { BasicPageParams } from '../../model/baseModel'

export type DetailModel = {
  id: bigint // 自增id
  unique_id: string // 随机唯一键
  game_id: number // 游戏ID
  game_id_name: string // 游戏ID
  name: string // 名称
  status: number // 状态
  start_time: number // 开始时间
  end_time: number // 结束时间
  award: string // 奖品配置，假设为JSON字符串
  award_type: number // 活动类型
  created_at: Date | null // 创建时间
  updated_at: Date | null // 更新时间
}

export type ListPageParams = BasicPageParams & DetailModel
