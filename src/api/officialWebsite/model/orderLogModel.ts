import { BasicPageParams } from '../../model/baseModel'

export type DetailModel = {
  id: bigint // 自增id
  game_id: number // 游戏id
  game_id_name: string
  unique_id: string // 用户唯一标识
  page_id: string // 用户唯一标识
  phone: string // 手机号
  recommend_phone: string // 推荐手机号
  recommend_dialing_code: string // 推荐手机号区号
  dialing_code: string // 手机区号
  recommend_utm: string // 来源渠道
  ip: string // ip地址
  created_at: Date | null // 创建时间
  updated_at: Date | null // 更新时间
  version: string // 版本记录
}

export type ListPageParams = BasicPageParams & DetailModel
