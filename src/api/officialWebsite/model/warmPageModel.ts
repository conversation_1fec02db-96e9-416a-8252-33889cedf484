import { BasicPageParams } from '../../model/baseModel'

export type DetailModel = {
  id: bigint // 自增id
  name: string // 预热页名称
  en_name: string // 英文简称
  game_id: number // 游戏ID
  game_id_name: string
  order_user_distinct: number // 预约人数
  real_order_user_distinct: number // 真实预约人数
  real_order_ip_distinct: number // IP去重预约人数
  start_time: number // 开始时间
  end_time: number // 结束时间
  active_id: string // 关联活动ID
  package_config: string // 关联礼包配置，假设为JSON字符串
  appstore_url: string // IOS预约链接
  google_play_url: string // 安卓预约链接
  share_pic_url: string // 分享图片链接
  share_title: string // 分享文案
  video_url: string // 视频链接
  sign_key: string // 签名key
  created_at: Date | null // 创建时间
  updated_at: Date | null // 更新时间
  share_description: string // 分享详情
  share_name: string // 分享名称
  assoc_url: string // 社群链接
  engagement_host: string // 官网地址
}

export type ListPageParams = BasicPageParams & DetailModel
