import { BasicPageParams } from '../../model/baseModel'

export type DetailModel = {
  id: bigint // 自增id
  unique_id: string // 随机唯一键
  name: string // 名称
  desc: string // 备注
  game_id: number // 游戏ID
  game_id_name: string
  count: number // 礼包码累计
  send_count: number // 领取数量
  status: number // 状态
  start_time: number // 开始时间
  end_time: number // 结束时间
  created_at: Date | null // 创建时间
  updated_at: Date | null // 更新时间
}

export type ListPageParams = BasicPageParams & DetailModel
