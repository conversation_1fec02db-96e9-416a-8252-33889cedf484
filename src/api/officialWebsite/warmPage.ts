import { ListPageParams, DetailModel } from './model/warmPageModel'

import { defHttp } from '/@/utils/http/axios'

enum Api {
  ListApi = '/officialWebsite/warmPage/index',
  SubmitApi = '/officialWebsite/warmPage/submit',
}

export const getListPage = (params: ListPageParams) =>
  defHttp.get<DetailModel>({ url: Api.ListApi, params })

export const Submit = (params: DetailModel) => defHttp.post({ url: Api.SubmitApi, params })
