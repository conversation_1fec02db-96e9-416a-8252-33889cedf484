import { ListPageParams, DetailModel } from './model/orderLogModel'

import { defHttp } from '/@/utils/http/axios'

enum Api {
  ListApi = '/officialWebsite/orderLog/index',
  SubmitApi = '/officialWebsite/orderLog/submit',
  StatApi = '/officialWebsite/orderLog/stat',
  StatUtmApi = '/officialWebsite/orderLog/statUtm',
  StatSourcePageApi = '/officialWebsite/orderLog/statSourcePage',
}

export const getListPage = (params: ListPageParams) =>
  defHttp.get<DetailModel>({ url: Api.ListApi, params })
export const StatListPage = (params: any) => defHttp.get<DetailModel>({ url: Api.StatApi, params })

export const StatUtmApiListPage = (params: ListPageParams) =>
  defHttp.get<any>({ url: Api.StatUtmApi, params })

export const StatPageApiListPage = (params: ListPageParams) =>
  defHttp.get<any>({ url: Api.StatSourcePageApi, params })

export const Submit = (params: DetailModel) => defHttp.post({ url: Api.SubmitApi, params })
