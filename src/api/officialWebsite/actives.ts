import { ListPageParams, DetailModel } from './model/activesModel'

import { defHttp } from '/@/utils/http/axios'

enum Api {
  ListApi = '/officialWebsite/actives/index',
  UpdateStatusApi = '/officialWebsite/actives/updateStatus',
  SubmitApi = '/officialWebsite/actives/submit',
}

export const getListPage = (params: ListPageParams) =>
  defHttp.get<DetailModel>({ url: Api.ListApi, params })

export const UpdateStatus = (id: number, status: number) =>
  defHttp.get<DetailModel>({ url: Api.UpdateStatusApi, params: { id: id, status: status } })

export const Submit = (params: DetailModel) => defHttp.post({ url: Api.SubmitApi, params })
