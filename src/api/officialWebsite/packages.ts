import { ListPageParams, DetailModel } from './model/packagesModel'
import { ContentTypeEnum } from '/@/enums/httpEnum'

import { defHttp } from '/@/utils/http/axios'

enum Api {
  ListApi = '/officialWebsite/packages/index',
  SubmitApi = '/officialWebsite/packages/submit',
  UpdateStatusApi = '/officialWebsite/packages/updateStatus',
}

export const getListPage = (params: ListPageParams) =>
  defHttp.get<DetailModel>({ url: Api.ListApi, params })

export const UpdateStatus = (id: number, status: number) =>
  defHttp.get<DetailModel>({ url: Api.UpdateStatusApi, params: { id: id, status: status } })

export const Submit = (params: any) => {
  return defHttp.post({
    url: Api.SubmitApi,
    headers: { 'Content-Type': ContentTypeEnum.FORM_DATA },
    data: params,
    // @ts-ignore
    ignoreCancelToken: true,
  })
}
