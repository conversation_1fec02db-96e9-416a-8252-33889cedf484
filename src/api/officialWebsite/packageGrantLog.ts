import { ListPageParams, DetailModel } from './model/packageGrantLogModel'

import { defHttp } from '/@/utils/http/axios'

enum Api {
  ListApi = '/officialWebsite/packageGrantLog/index',
  SubmitApi = '/officialWebsite/packageGrantLog/submit',
}

export const getListPage = (params: ListPageParams) =>
  defHttp.get<DetailModel>({ url: Api.ListApi, params })

export const Submit = (params: DetailModel) => defHttp.post({ url: Api.SubmitApi, params })
