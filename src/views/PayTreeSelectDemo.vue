<template>
  <div class="demo-container">
    <h1>PayTreeSelect 组件演示 - 父级节点自动展开为叶子节点</h1>

    <div class="demo-section">
      <h2>基础用法（使用内置假数据）</h2>
      <PayTreeSelect
        v-model="selectedValues1"
        :options="[]"
        placeholder="请选择支付方式"
        @change="handleChange1"
      />
      <div class="result-display">
        <p><strong>v-model 返回值:</strong> {{ JSON.stringify(selectedValues1) }}</p>
        <p><strong>叶子节点标签:</strong> {{ getDisplayLabels(selectedValues1) }}</p>
      </div>
    </div>

    <div class="demo-section">
      <h2>自定义数据测试</h2>
      <PayTreeSelect
        v-model="selectedValues2"
        :options="customData"
        placeholder="请选择游戏类型"
        @change="handleChange2"
      />
      <div class="result-display">
        <p><strong>v-model 返回值:</strong> {{ JSON.stringify(selectedValues2) }}</p>
        <p><strong>叶子节点标签:</strong> {{ getCustomDisplayLabels(selectedValues2) }}</p>
      </div>
    </div>

    <div class="demo-section">
      <h2>功能验证</h2>
      <ul>
        <li>✅ 所有节点都可以被点击选择（包括父级节点和叶子节点）</li>
        <li>✅ 选择父级节点时，自动选中其下所有叶子节点</li>
        <li>✅ 取消选择父级节点时，自动取消其下所有叶子节点</li>
        <li>✅ v-model 只返回叶子节点的 value 值</li>
        <li>✅ 显示框只显示叶子节点的标签，不显示完整路径</li>
        <li>✅ 支持搜索功能</li>
        <li>✅ 支持多选和清空选择</li>
      </ul>

      <div class="usage-tips">
        <h3>使用提示：</h3>
        <p>• 点击"在线支付"可以快速选择所有在线支付方式</p>
        <p>• 点击"第三方支付"可以快速选择支付宝、微信支付、银联支付</p>
        <p>• 也可以单独选择/取消选择具体的支付方式</p>
        <p>• 最终返回值始终只包含叶子节点，便于数据处理</p>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref } from 'vue'
  import PayTreeSelect from '@/components/Pay/PayTreeSelect.vue'

  // 响应式数据
  const selectedValues1 = ref([])
  const selectedValues2 = ref([])

  // 自定义数据示例
  const customData = [
    {
      value: 'action',
      label: '动作游戏',
      children: [
        {
          value: 'fps',
          label: '第一人称射击',
          children: [
            { value: 'csgo', label: 'CS:GO' },
            { value: 'valorant', label: 'Valorant' },
            { value: 'overwatch', label: '守望先锋' },
          ],
        },
        {
          value: 'fighting',
          label: '格斗游戏',
          children: [
            { value: 'sf6', label: '街霸6' },
            { value: 'tekken8', label: '铁拳8' },
          ],
        },
      ],
    },
    {
      value: 'rpg',
      label: '角色扮演',
      children: [
        { value: 'persona5', label: '女神异闻录5' },
        { value: 'nier', label: '尼尔：自动人形' },
      ],
    },
  ]

  // 内置假数据的标签映射
  const builtInLabels: Record<string, string> = {
    alipay: '支付宝',
    wechat: '微信支付',
    unionpay: '银联支付',
    icbc: '工商银行',
    ccb: '建设银行',
    abc: '农业银行',
    boc: '中国银行',
    rmb: '人民币',
    usd: '美元',
    'credit-card': '信用卡',
    'debit-card': '借记卡',
    bitcoin: '比特币',
    ethereum: '以太坊',
    usdt: 'USDT',
  }

  // 自定义数据的标签映射
  const customLabels: Record<string, string> = {
    csgo: 'CS:GO',
    valorant: 'Valorant',
    overwatch: '守望先锋',
    sf6: '街霸6',
    tekken8: '铁拳8',
    persona5: '女神异闻录5',
    nier: '尼尔：自动人形',
  }

  // 获取显示标签
  const getDisplayLabels = (values: any[]) => {
    return values.map((val) => builtInLabels[val] || val).join(', ')
  }

  const getCustomDisplayLabels = (values: any[]) => {
    return values.map((val) => customLabels[val] || val).join(', ')
  }

  // 事件处理函数
  const handleChange1 = (values: any, selectedOptions: any) => {
    console.log('基础用法 - 选中的叶子节点值:', values)
    console.log('基础用法 - 选中的选项:', selectedOptions)
  }

  const handleChange2 = (values: any, selectedOptions: any) => {
    console.log('自定义数据 - 选中的叶子节点值:', values)
    console.log('自定义数据 - 选中的选项:', selectedOptions)
  }
</script>

<style scoped>
  .demo-container {
    padding: 20px;
    max-width: 900px;
    margin: 0 auto;
  }

  .demo-section {
    margin-bottom: 30px;
    padding: 20px;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    background-color: #fafafa;
  }

  .demo-section h2 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #333;
    font-size: 16px;
  }

  .result-display {
    margin-top: 15px;
    padding: 15px;
    background-color: #f0f0f0;
    border-radius: 4px;
    border-left: 4px solid #1890ff;
  }

  .result-display p {
    margin: 8px 0;
    font-family: monospace;
    font-size: 14px;
  }

  .result-display strong {
    color: #1890ff;
  }

  h1 {
    text-align: center;
    color: #1890ff;
    margin-bottom: 30px;
  }

  ul {
    margin: 15px 0;
    padding-left: 20px;
  }

  li {
    margin: 8px 0;
    color: #52c41a;
  }
</style>
