import { BasicColumn } from '/@/components/Table'
import { FormSchema } from '/@/components/Table'
import { getListPage as projectListPage } from '/@/api/sdk/project'
import { dateRanges } from '/@/utils/helper/dateRanges'
import { getListPage as appListPage } from '/@/api/sdk/app'

export const dimColumns: any[] = [
  {
    title: '项目',
    groupBy: 'project_id',
    dataIndex: 'project_id_name',
    width: 150,
    fixed: 'left',
  },
  {
    title: '地区',
    groupBy: 'country_code',
    dataIndex: 'country_code_name',
    width: 120,
    fixed: 'left',
  },
  {
    title: '手机型号',
    groupBy: 'model',
    dataIndex: 'model',
    width: 120,
    fixed: 'left',
  },
  {
    title: '手机制造商',
    groupBy: 'manufacturer',
    dataIndex: 'manufacturer',
    width: 120,
    fixed: 'left',
  },
  {
    title: '系统版本号',
    groupBy: 'os_ver',
    dataIndex: 'os_ver',
    width: 120,
    fixed: 'left',
  },
  {
    title: '设备类型',
    groupBy: 'device_type',
    dataIndex: 'device_type',
    width: 120,
    fixed: 'left',
  },
  {
    title: '手机语言',
    groupBy: 'language',
    dataIndex: 'language',
    width: 120,
    fixed: 'left',
  },
]

export const columns: BasicColumn[] = [
  {
    title: '注册用户数',
    dataIndex: 'reg_cnt',
    width: 100,
    sorter: true,
    showSorterTooltip: false,
  },
  {
    title: '下单笔数',
    dataIndex: 'create_order_cnt',
    width: 100,
    sorter: true,
    showSorterTooltip: false,
  },
  {
    title: '支付成功用户数',
    dataIndex: 'pay_success_uer_cnt',
    width: 100,
    sorter: true,
    showSorterTooltip: false,
  },
  {
    title: '支付成功金额',
    dataIndex: 'pay_success_money_usd',
    width: 100,
    sorter: true,
    showSorterTooltip: false,
  },
  { title: '充值占比', dataIndex: 'pay_success_money_pct', width: 100 },
]

export const searchFormSchema: FormSchema[] = [
  {
    component: 'RangePicker',
    // label: '选择时间',
    field: '[created_at_start, created_at_end]',
    colProps: { span: 4 },
    defaultValue: dateRanges.Today,
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
      allowClear: false,
      placeholder: ['开始时间', '结束时间'],
    },
  },
  {
    field: 'project_id[]',
    component: 'ApiSelect',
    colProps: { span: 4 },
    componentProps: {
      mode: 'multiple',
      placeholder: '请选择项目',
      maxTagCount: 1,
      showSearch: true,
      api: () => {
        return projectListPage({ page_size: 9999, page: 1 })
      },
      labelField: 'name',
      valueField: 'id',
    },
  },
  {
    field: 'appid[]',
    component: 'ApiSelect',
    colProps: { span: 3 },
    componentProps: {
      mode: 'multiple',
      placeholder: '请选择应用',
      maxTagCount: 1,
      showSearch: true,
      api: () => {
        return appListPage({ page_size: 9999, page: 1 })
      },
      labelField: 'name',
      valueField: 'appid',
    },
  },
  {
    field: 'show',
    colProps: { span: 22 },
    label: '维度:',
    component: 'RadioGroup',
    defaultValue: 'project_id',
    componentProps: {
      options: dimColumns.map((c) => ({ label: c.title, value: c.groupBy })),
    },
  },
]
