<template>
  <Dropdown
    placement="bottom"
    :trigger="['click']"
    :dropMenuList="areasList"
    :selectedKeys="selectedKeys"
    @menu-event="handleMenuEvent"
    overlayClassName="app-locale-picker-overlay"
  >
    <span class="cursor-pointer flex items-center">
      <Icon icon="ant-design:database-twotone" />
      {{ getSdkAreaText }}
      <span v-if="showText" class="ml-1">{{ getSdkAreaText }}</span>
    </span>
  </Dropdown>
</template>
<script lang="ts" setup>
  import { ref, watchEffect, unref, computed } from 'vue'
  import { Dropdown } from '/@/components/Dropdown'
  import { Icon } from '/@/components/Icon'
  import { useSdkSelectAreaStoreWithOut } from '/@/store/modules/sdkSelectArea'
  import { areasList } from './areasListCofing'

  // const areasList = [
  //   { text: '台湾', event: 'TW', mysql: 'sdk_mysql_tw' },
  //   { text: '香港', event: 'HK', mysql: 'sdk_mysql_hk' },
  //   // { text: '欧美', event: 'ER', mysql: 'sdk_mysql_er' },
  // ]

  const props = defineProps({
    /**
     * Whether to display text
     */
    showText: { type: Boolean, default: true },
    /**
     * Whether to refresh the interface when changing
     */
    reload: { type: Boolean },
  })

  const selectedKeys = ref<string[]>([])

  const { setSelectSdkArea, getSelectArea } = useSdkSelectAreaStoreWithOut()

  const getSdkAreaText = computed(() => {
    const key = selectedKeys.value[0]
    if (!key) {
      return ''
    }
    return areasList.find((item) => item.event === key)?.text
  })

  watchEffect(() => {
    selectedKeys.value = [unref(getSelectArea)]
  })

  function handleMenuEvent(menu) {
    setSelectSdkArea(menu.event)
    props.reload && location.reload()
  }
</script>

<style lang="less">
  .app-locale-picker-overlay {
    .ant-dropdown-menu-item {
      min-width: 160px;
    }
  }
</style>
