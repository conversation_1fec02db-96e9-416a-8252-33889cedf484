<script lang="ts" setup>
  import { Cascader } from 'ant-design-vue'
  import { ref, computed, watch } from 'vue'

  // 定义选项数据类型
  interface TreeOption {
    [key: string]: any
    children?: TreeOption[]
  }

  // 定义字段名配置类型
  interface FieldNames {
    label?: string
    value?: string
    children?: string
  }

  // Props 定义
  interface Props {
    modelValue?: (string | number)[]
    options: TreeOption[]
    fieldNames?: FieldNames
    placeholder?: string
    disabled?: boolean
  }

  // Events 定义
  interface Emits {
    (e: 'update:modelValue', value: (string | number)[]): void
    (e: 'change', value: (string | number)[], selectedOptions: TreeOption[]): void
  }

  const props = withDefaults(defineProps<Props>(), {
    modelValue: () => [],
    fieldNames: () => ({ label: 'label', value: 'value', children: 'children' }),
    placeholder: '请选择支付方式',
    disabled: false,
  })

  const emit = defineEmits<Emits>()

  // 假数据用于测试
  const mockData: TreeOption[] = [
    {
      value: 'online',
      label: '在线支付',
      children: [
        {
          value: 'third-party',
          label: '第三方支付',
          children: [
            { value: 'alipay', label: '支付宝' },
            { value: 'wechat', label: '微信支付' },
            { value: 'unionpay', label: '银联支付' },
          ],
        },
        {
          value: 'bank',
          label: '银行卡支付',
          children: [
            { value: 'icbc', label: '工商银行' },
            { value: 'ccb', label: '建设银行' },
            { value: 'abc', label: '农业银行' },
            { value: 'boc', label: '中国银行' },
          ],
        },
      ],
    },
    {
      value: 'offline',
      label: '线下支付',
      children: [
        {
          value: 'cash',
          label: '现金支付',
          children: [
            { value: 'rmb', label: '人民币' },
            { value: 'usd', label: '美元' },
          ],
        },
        {
          value: 'pos',
          label: 'POS机支付',
          children: [
            { value: 'credit-card', label: '信用卡' },
            { value: 'debit-card', label: '借记卡' },
          ],
        },
      ],
    },
    {
      value: 'crypto',
      label: '数字货币',
      children: [
        { value: 'bitcoin', label: '比特币' },
        { value: 'ethereum', label: '以太坊' },
        { value: 'usdt', label: 'USDT' },
      ],
    },
  ]

  // 内部值管理
  const internalValue = ref<(string | number)[]>([])

  // 如果没有传入 options，使用假数据
  const finalOptions = computed(() => (props.options.length > 0 ? props.options : mockData))

  // 监听外部值变化
  watch(
    () => props.modelValue,
    (newValue) => {
      internalValue.value = newValue || []
    },
    { immediate: true },
  )

  // 处理字段名映射
  const normalizedOptions = computed(() => {
    const { label = 'label', value = 'value', children = 'children' } = props.fieldNames

    const normalizeNode = (node: TreeOption): TreeOption => {
      const normalized: TreeOption = {
        label: node[label],
        value: node[value],
        disabled: false,
      }

      // 如果有子节点，递归处理
      if (node[children] && Array.isArray(node[children]) && node[children].length > 0) {
        normalized.children = node[children].map(normalizeNode)
        // 非叶子节点设为禁用（不可选择）
        normalized.disabled = false
      }

      return normalized
    }

    return finalOptions.value.map(normalizeNode)
  })

  // 过滤函数 - 支持搜索
  const filter = (inputValue: string, path: TreeOption[]) => {
    return path.some((option) =>
      option.label?.toString().toLowerCase().includes(inputValue.toLowerCase()),
    )
  }

  // 处理值变化
  const handleChange = (value: (string | number)[], selectedOptions: TreeOption[]) => {
    console.log(selectedOptions)
    internalValue.value = value
    emit('update:modelValue', value)
    emit('change', value, selectedOptions)
  }

  // Cascader 配置
  const cascaderProps = computed(() => ({
    value: internalValue.value,
    options: normalizedOptions.value,
    placeholder: props.placeholder,
    disabled: props.disabled,
    multiple: true,
    showSearch: { filter },
    displayRender: ({ labels }: { labels: string[] }) => labels.join(' / '),
    maxTagCount: 'responsive' as const,
    allowClear: true,
    showCheckedStrategy:
  }))
</script>

<template>
  <Cascader v-bind="cascaderProps" @change="handleChange" class="pay-tree-select" />
</template>

<style scoped>
  .pay-tree-select {
    width: 100%;
  }

  .pay-tree-select :deep(.ant-cascader-picker) {
    width: 100%;
  }

  .pay-tree-select :deep(.ant-cascader-selection-item) {
    max-width: 150px;
  }
</style>
