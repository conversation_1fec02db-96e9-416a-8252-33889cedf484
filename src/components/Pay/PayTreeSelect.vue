<script lang="ts" setup>
  import { Cascader } from 'ant-design-vue'
  import { ref, computed, watch } from 'vue'

  // 定义选项数据类型
  interface TreeOption {
    [key: string]: any
    children?: TreeOption[]
  }

  // 定义字段名配置类型
  interface FieldNames {
    label?: string
    value?: string
    children?: string
  }

  // Props 定义
  interface Props {
    modelValue?: (string | number)[]
    options: TreeOption[]
    fieldNames?: FieldNames
    placeholder?: string
    disabled?: boolean
  }

  // Events 定义
  interface Emits {
    (e: 'update:modelValue', value: (string | number)[]): void
    (e: 'change', value: (string | number)[], selectedOptions: TreeOption[]): void
  }

  const props = withDefaults(defineProps<Props>(), {
    modelValue: () => [],
    fieldNames: () => ({ label: 'label', value: 'value', children: 'children' }),
    placeholder: '请选择支付方式',
    disabled: false,
  })

  const emit = defineEmits<Emits>()

  // 假数据用于测试
  const mockData: TreeOption[] = [
    {
      value: 'online',
      label: '在线支付',
      children: [
        {
          value: 'third-party',
          label: '第三方支付',
          children: [
            { value: 'alipay', label: '支付宝' },
            { value: 'wechat', label: '微信支付' },
            { value: 'unionpay', label: '银联支付' },
          ],
        },
        {
          value: 'bank',
          label: '银行卡支付',
          children: [
            { value: 'icbc', label: '工商银行' },
            { value: 'ccb', label: '建设银行' },
            { value: 'abc', label: '农业银行' },
            { value: 'boc', label: '中国银行' },
          ],
        },
      ],
    },
    {
      value: 'offline',
      label: '线下支付',
      children: [
        {
          value: 'cash',
          label: '现金支付',
          children: [
            { value: 'rmb', label: '人民币' },
            { value: 'usd', label: '美元' },
          ],
        },
        {
          value: 'pos',
          label: 'POS机支付',
          children: [
            { value: 'credit-card', label: '信用卡' },
            { value: 'debit-card', label: '借记卡' },
          ],
        },
      ],
    },
    {
      value: 'crypto',
      label: '数字货币',
      children: [
        { value: 'bitcoin', label: '比特币' },
        { value: 'ethereum', label: '以太坊' },
        { value: 'usdt', label: 'USDT' },
      ],
    },
  ]

  // 内部值管理 - 存储叶子节点值
  const internalValue = ref<(string | number)[]>([])

  // 内部 Cascader 值管理 - 存储路径数组
  const cascaderValue = ref<(string | number)[][]>([])

  // 如果没有传入 options，使用假数据
  const finalOptions = computed(() => (props.options.length > 0 ? props.options : mockData))

  // 根据叶子节点值生成 Cascader 需要的路径数组
  const generateCascaderPaths = (leafValues: (string | number)[]): (string | number)[][] => {
    const paths: (string | number)[][] = []

    leafValues.forEach((leafValue) => {
      const path = findPathToValue(leafValue, finalOptions.value, props.fieldNames)
      if (path.length > 0) {
        paths.push(path)
      }
    })

    return paths
  }

  // 查找到指定值的路径
  const findPathToValue = (
    targetValue: string | number,
    nodes: TreeOption[],
    fieldNames: FieldNames,
  ): (string | number)[] => {
    const { value = 'value', children = 'children' } = fieldNames

    const findPath = (
      nodeList: TreeOption[],
      currentPath: (string | number)[],
    ): (string | number)[] => {
      for (const node of nodeList) {
        const newPath = [...currentPath, node[value]]

        if (node[value] === targetValue) {
          return newPath
        }

        if (node[children] && Array.isArray(node[children])) {
          const foundPath = findPath(node[children], newPath)
          if (foundPath.length > 0) {
            return foundPath
          }
        }
      }
      return []
    }

    return findPath(nodes, [])
  }

  // 监听外部值变化
  watch(
    () => props.modelValue,
    (newValue) => {
      internalValue.value = newValue || []
      cascaderValue.value = generateCascaderPaths(newValue || [])
    },
    { immediate: true },
  )

  // 处理字段名映射
  const normalizedOptions = computed(() => {
    const { label = 'label', value = 'value', children = 'children' } = props.fieldNames

    const normalizeNode = (node: TreeOption): TreeOption => {
      const normalized: TreeOption = {
        label: node[label],
        value: node[value],
        disabled: false,
      }

      // 如果有子节点，递归处理
      if (node[children] && Array.isArray(node[children]) && node[children].length > 0) {
        normalized.children = node[children].map(normalizeNode)
        // 父级节点保持可点击，通过 changeOnSelect: false 来控制只能选择叶子节点
      }

      return normalized
    }

    return finalOptions.value.map(normalizeNode)
  })

  // 获取所有叶子节点的工具函数
  const getAllLeafNodes = (nodes: TreeOption[], fieldNames: FieldNames): TreeOption[] => {
    const { label = 'label', value = 'value', children = 'children' } = fieldNames
    const leafNodes: TreeOption[] = []

    const traverse = (nodeList: TreeOption[]) => {
      nodeList.forEach((node) => {
        if (!node[children] || !Array.isArray(node[children]) || node[children].length === 0) {
          // 这是叶子节点
          leafNodes.push({
            label: node[label],
            value: node[value],
          })
        } else {
          // 递归处理子节点
          traverse(node[children])
        }
      })
    }

    traverse(nodes)
    return leafNodes
  }

  // 获取指定节点下的所有叶子节点
  const getLeafNodesUnderParent = (
    parentValue: string | number,
    nodes: TreeOption[],
    fieldNames: FieldNames,
  ): TreeOption[] => {
    const { value = 'value', children = 'children' } = fieldNames

    const findParentNode = (nodeList: TreeOption[]): TreeOption | null => {
      for (const node of nodeList) {
        if (node[value] === parentValue) {
          return node
        }
        if (node[children] && Array.isArray(node[children])) {
          const found = findParentNode(node[children])
          if (found) return found
        }
      }
      return null
    }

    const parentNode = findParentNode(nodes)
    if (!parentNode || !parentNode[children]) {
      return []
    }

    return getAllLeafNodes([parentNode], fieldNames)
  }

  // 判断一个节点是否为叶子节点
  const isLeafNode = (
    nodeValue: string | number,
    nodes: TreeOption[],
    fieldNames: FieldNames,
  ): boolean => {
    const { value = 'value', children = 'children' } = fieldNames

    const findNode = (nodeList: TreeOption[]): TreeOption | null => {
      for (const node of nodeList) {
        if (node[value] === nodeValue) {
          return node
        }
        if (node[children] && Array.isArray(node[children])) {
          const found = findNode(node[children])
          if (found) return found
        }
      }
      return null
    }

    const node = findNode(nodes)
    return !node || !node[children] || !Array.isArray(node[children]) || node[children].length === 0
  }

  // 根据叶子节点值获取标签的工具函数
  const getLeafNodeLabels = (values: (string | number)[]): string[] => {
    const allLeafNodes = getAllLeafNodes(finalOptions.value, props.fieldNames)
    return values
      .map((value) => {
        const leafNode = allLeafNodes.find((node) => node.value === value)
        return leafNode ? leafNode.label : ''
      })
      .filter((label) => label !== '')
  }

  // 过滤函数 - 支持搜索
  const filter = (inputValue: string, path: TreeOption[]) => {
    return path.some((option) =>
      option.label?.toString().toLowerCase().includes(inputValue.toLowerCase()),
    )
  }

  // 处理值变化
  const handleChange = (value: any, selectedOptions: TreeOption[]) => {
    console.log('原始选中值:', value)

    // Cascader 多选模式返回的是路径数组的数组，需要转换
    // 例如：[['online', 'bank', 'boc'], ['online', 'third-party', 'alipay']]
    const pathArrays: (string | number)[][] = Array.isArray(value) ? value : []

    // 提取所有选中的值（包括路径中的所有值）
    const allSelectedValues: (string | number)[] = []
    pathArrays.forEach((path) => {
      if (Array.isArray(path)) {
        allSelectedValues.push(...path)
      }
    })

    // 去重
    const uniqueSelectedValues = [...new Set(allSelectedValues)]

    console.log('解析后的选中值:', uniqueSelectedValues)

    // 获取当前所有叶子节点
    const allLeafNodes = getAllLeafNodes(finalOptions.value, props.fieldNames)
    const allLeafNodeValues = allLeafNodes.map((node) => node.value)

    // 获取之前的叶子节点值
    const previousLeafValues = internalValue.value

    // 分析新增和移除的值
    const newValues = uniqueSelectedValues.filter((val) => !previousLeafValues.includes(val))
    const removedValues = previousLeafValues.filter((val) => !uniqueSelectedValues.includes(val))

    let finalLeafValues = [...previousLeafValues]

    // 处理新增的值
    newValues.forEach((newValue) => {
      if (isLeafNode(newValue, finalOptions.value, props.fieldNames)) {
        // 如果是叶子节点，直接添加
        if (!finalLeafValues.includes(newValue)) {
          finalLeafValues.push(newValue)
        }
      } else {
        // 如果是父级节点，添加其下所有叶子节点
        const childLeafNodes = getLeafNodesUnderParent(
          newValue,
          finalOptions.value,
          props.fieldNames,
        )
        childLeafNodes.forEach((leafNode) => {
          if (!finalLeafValues.includes(leafNode.value)) {
            finalLeafValues.push(leafNode.value)
          }
        })
      }
    })

    // 处理移除的值
    removedValues.forEach((removedValue) => {
      if (isLeafNode(removedValue, finalOptions.value, props.fieldNames)) {
        // 如果是叶子节点，直接移除
        finalLeafValues = finalLeafValues.filter((val) => val !== removedValue)
      } else {
        // 如果是父级节点，移除其下所有叶子节点
        const childLeafNodes = getLeafNodesUnderParent(
          removedValue,
          finalOptions.value,
          props.fieldNames,
        )
        const childLeafValues = childLeafNodes.map((node) => node.value)
        finalLeafValues = finalLeafValues.filter((val) => !childLeafValues.includes(val))
      }
    })

    // 确保只包含叶子节点
    finalLeafValues = finalLeafValues.filter((val) => allLeafNodeValues.includes(val))

    console.log('最终叶子节点值:', finalLeafValues)

    internalValue.value = finalLeafValues
    emit('update:modelValue', finalLeafValues)
    emit('change', finalLeafValues, selectedOptions)
  }

  // 自定义显示渲染函数 - 只显示叶子节点标签
  const customDisplayRender = ({
    labels: _labels,
    selectedOptions,
  }: {
    labels: string[]
    selectedOptions?: TreeOption[]
  }) => {
    // 如果有选中的选项，获取叶子节点的标签
    if (selectedOptions && selectedOptions.length > 0) {
      const leafLabels = getLeafNodeLabels(internalValue.value)
      return leafLabels.join(', ')
    }
    // 如果没有选中项，返回空字符串
    return ''
  }

  // Cascader 配置
  const cascaderProps = computed(() => ({
    value: internalValue.value,
    options: normalizedOptions.value,
    placeholder: props.placeholder,
    disabled: props.disabled,
    multiple: true,
    showSearch: { filter },
    displayRender: customDisplayRender,
    maxTagCount: 'responsive' as const,
    allowClear: true,
  }))
</script>

<template>
  <Cascader v-bind="cascaderProps" @change="handleChange" class="pay-tree-select" />
</template>

<style scoped>
  .pay-tree-select {
    width: 100%;
  }

  .pay-tree-select :deep(.ant-cascader-picker) {
    width: 100%;
  }

  .pay-tree-select :deep(.ant-cascader-selection-item) {
    max-width: 150px;
  }
</style>
