<script lang="ts" setup>
  import { Cascader } from 'ant-design-vue'
  import { ref, computed, watch } from 'vue'

  // 定义选项数据类型
  interface TreeOption {
    [key: string]: any
    children?: TreeOption[]
  }

  // 定义字段名配置类型
  interface FieldNames {
    label?: string
    value?: string
    children?: string
  }

  // Props 定义
  interface Props {
    modelValue?: (string | number)[]
    options: TreeOption[]
    fieldNames?: FieldNames
    placeholder?: string
    disabled?: boolean
  }

  // Events 定义
  interface Emits {
    (e: 'update:modelValue', value: (string | number)[]): void
    (e: 'change', value: (string | number)[], selectedOptions: TreeOption[]): void
  }

  const props = withDefaults(defineProps<Props>(), {
    modelValue: () => [],
    fieldNames: () => ({ label: 'label', value: 'value', children: 'children' }),
    placeholder: '请选择',
    disabled: false,
  })

  const emit = defineEmits<Emits>()

  // 内部值管理
  const internalValue = ref<(string | number)[]>([])

  // 监听外部值变化
  watch(
    () => props.modelValue,
    (newValue) => {
      internalValue.value = newValue || []
    },
    { immediate: true },
  )

  // 处理字段名映射
  const normalizedOptions = computed(() => {
    const { label = 'label', value = 'value', children = 'children' } = props.fieldNames

    const normalizeNode = (node: TreeOption): TreeOption => {
      const normalized: TreeOption = {
        label: node[label],
        value: node[value],
        disabled: false,
      }

      // 如果有子节点，递归处理
      if (node[children] && Array.isArray(node[children]) && node[children].length > 0) {
        normalized.children = node[children].map(normalizeNode)
        // 非叶子节点设为禁用（不可选择）
        normalized.disabled = true
      }

      return normalized
    }

    return props.options.map(normalizeNode)
  })

  // 过滤函数 - 支持搜索
  const filter = (inputValue: string, path: TreeOption[]) => {
    return path.some((option) =>
      option.label?.toString().toLowerCase().includes(inputValue.toLowerCase()),
    )
  }

  // 处理值变化
  const handleChange = (value: (string | number)[], selectedOptions: TreeOption[]) => {
    internalValue.value = value
    emit('update:modelValue', value)
    emit('change', value, selectedOptions)
  }

  // Cascader 配置
  const cascaderProps = computed(() => ({
    value: internalValue.value,
    options: normalizedOptions.value,
    placeholder: props.placeholder,
    disabled: props.disabled,
    multiple: true,
    showSearch: { filter },
    changeOnSelect: false, // 只允许选择叶子节点
    displayRender: (labels: string[]) => labels.join(' / '),
    maxTagCount: 'responsive' as const,
    allowClear: true,
  }))
</script>

<template>
  <Cascader v-bind="cascaderProps" @change="handleChange" class="pay-tree-select" />
</template>

<style scoped>
  .pay-tree-select {
    width: 100%;
  }

  .pay-tree-select :deep(.ant-cascader-picker) {
    width: 100%;
  }

  .pay-tree-select :deep(.ant-cascader-selection-item) {
    max-width: 150px;
  }
</style>
